package de.sarocesch.sarosrecipe.client.gui;

import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

import de.sarocesch.sarosrecipe.network.NetworkHandler;
import de.sarocesch.sarosrecipe.network.packets.SelectModPacket;
import de.sarocesch.sarosrecipe.services.ModDataService;

import java.util.List;

/**
 * GUI screen for selecting a mod on first-time join
 */
@OnlyIn(Dist.CLIENT)
public class ModSelectionScreen extends Screen {
    
    private static final int MODS_PER_PAGE = 12; // 4x3 grid
    private static final int MOD_BUTTON_WIDTH = 140;
    private static final int MOD_BUTTON_HEIGHT = 80;
    private static final int BUTTON_SPACING = 15;
    
    private List<ModDataService.ModInfo> availableMods;
    private int currentPage = 0;
    private int totalPages;
    
    private Button prevPageButton;
    private Button nextPageButton;
    
    public ModSelectionScreen() {
        super(Component.translatable("gui.sarosrecipe.mod_selection.title"));
        this.availableMods = ModDataService.getModsWithRecipes();
        this.totalPages = (int) Math.ceil((double) availableMods.size() / MODS_PER_PAGE);
    }
    
    @Override
    protected void init() {
        super.init();
        
        // Clear existing widgets
        this.clearWidgets();
        
        // Calculate grid layout
        int startX = (this.width - (4 * MOD_BUTTON_WIDTH + 3 * BUTTON_SPACING)) / 2;
        int startY = (this.height - (3 * MOD_BUTTON_HEIGHT + 2 * BUTTON_SPACING)) / 2;
        
        // Add mod selection buttons for current page
        int modsOnThisPage = Math.min(MODS_PER_PAGE, availableMods.size() - (currentPage * MODS_PER_PAGE));
        
        for (int i = 0; i < modsOnThisPage; i++) {
            int modIndex = currentPage * MODS_PER_PAGE + i;
            ModDataService.ModInfo modInfo = availableMods.get(modIndex);
            
            int row = i / 4;
            int col = i % 4;
            
            int buttonX = startX + col * (MOD_BUTTON_WIDTH + BUTTON_SPACING);
            int buttonY = startY + row * (MOD_BUTTON_HEIGHT + BUTTON_SPACING);
            
            ModSelectionButton modButton = new ModSelectionButton(
                buttonX, buttonY, MOD_BUTTON_WIDTH, MOD_BUTTON_HEIGHT,
                modInfo, this::onModSelected
            );
            
            this.addRenderableWidget(modButton);
        }
        
        // Add pagination buttons if needed (with better spacing)
        if (totalPages > 1) {
            int paginationY = startY + 3 * (MOD_BUTTON_HEIGHT + BUTTON_SPACING) + 35;

            prevPageButton = Button.builder(
                Component.translatable("gui.sarosrecipe.mod_selection.prev_page"),
                button -> previousPage()
            ).bounds(this.width / 2 - 100, paginationY, 80, 20).build();

            nextPageButton = Button.builder(
                Component.translatable("gui.sarosrecipe.mod_selection.next_page"),
                button -> nextPage()
            ).bounds(this.width / 2 + 20, paginationY, 80, 20).build();

            this.addRenderableWidget(prevPageButton);
            this.addRenderableWidget(nextPageButton);

            updatePaginationButtons();
        }
    }
    
    private void onModSelected(ModDataService.ModInfo modInfo) {
        // Send packet to server with selected mod
        NetworkHandler.INSTANCE.sendToServer(new SelectModPacket(modInfo.getModId()));
        
        // Close the screen
        this.onClose();
    }
    
    private void previousPage() {
        if (currentPage > 0) {
            currentPage--;
            init();
        }
    }
    
    private void nextPage() {
        if (currentPage < totalPages - 1) {
            currentPage++;
            init();
        }
    }
    
    private void updatePaginationButtons() {
        if (prevPageButton != null) {
            prevPageButton.active = currentPage > 0;
        }
        if (nextPageButton != null) {
            nextPageButton.active = currentPage < totalPages - 1;
        }
    }
    
    @Override
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
        this.renderBackground(guiGraphics);

        // Render title with emphasis
        guiGraphics.drawCenteredString(this.font, this.title, this.width / 2, 20, 0xFFFF55);

        // Render subtitle with warning color
        Component subtitle = Component.translatable("gui.sarosrecipe.mod_selection.subtitle");
        guiGraphics.drawCenteredString(this.font, subtitle, this.width / 2, 35, 0xFFAA55);

        // Add mandatory selection notice
        Component notice = Component.literal("(Selection is required - ESC disabled)");
        guiGraphics.drawCenteredString(this.font, notice, this.width / 2, 50, 0xFF5555);

        // Render page info if multiple pages (moved further down)
        if (totalPages > 1) {
            Component pageInfo = Component.translatable("gui.sarosrecipe.mod_selection.page_info",
                currentPage + 1, totalPages);
            guiGraphics.drawCenteredString(this.font, pageInfo, this.width / 2, this.height - 25, 0xCCCCCC);
        }

        super.render(guiGraphics, mouseX, mouseY, partialTick);

        // Render tooltips for mod buttons
        for (var widget : this.renderables) {
            if (widget instanceof ModSelectionButton modButton && modButton.isHovered()) {
                modButton.renderTooltip(guiGraphics, mouseX, mouseY);
                break; // Only show one tooltip at a time
            }
        }
    }
    
    @Override
    public boolean isPauseScreen() {
        return false; // Don't pause the game
    }

    @Override
    public boolean shouldCloseOnEsc() {
        return false; // Prevent closing with ESC key
    }

    @Override
    public void onClose() {
        // Prevent closing - do nothing
        // Players must select a mod to continue
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Block ESC key and other closing keys
        if (keyCode == 256) { // ESC key
            return true; // Consume the key event without closing
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    /**
     * Custom button for mod selection with icon and text
     */
    private class ModSelectionButton extends Button {
        private final ModDataService.ModInfo modInfo;

        public ModSelectionButton(int x, int y, int width, int height,
                                ModDataService.ModInfo modInfo,
                                java.util.function.Consumer<ModDataService.ModInfo> onPress) {
            super(x, y, width, height, Component.literal(modInfo.getDisplayName()),
                  button -> onPress.accept(modInfo), DEFAULT_NARRATION);
            this.modInfo = modInfo;
        }

        @Override
        public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
            // Render button background
            super.render(guiGraphics, mouseX, mouseY, partialTick);

            // Render mod icon
            ItemStack icon = modInfo.getIcon();
            if (!icon.isEmpty()) {
                guiGraphics.renderItem(icon, this.getX() + 8, this.getY() + 8);
            }

            // Render mod display name (with better truncation)
            String displayName = modInfo.getDisplayName();
            int maxDisplayNameWidth = this.width - 35;
            String truncatedDisplayName = font.plainSubstrByWidth(displayName, maxDisplayNameWidth);
            if (!truncatedDisplayName.equals(displayName)) {
                truncatedDisplayName = font.plainSubstrByWidth(displayName, maxDisplayNameWidth - font.width("...")) + "...";
            }

            guiGraphics.drawString(font, truncatedDisplayName, this.getX() + 30, this.getY() + 12, 0xFFFFFF);

            // Render mod ID (with better truncation)
            String modId = modInfo.getModId();
            int maxModIdWidth = this.width - 35;
            String truncatedModId = font.plainSubstrByWidth(modId, maxModIdWidth);
            if (!truncatedModId.equals(modId)) {
                truncatedModId = font.plainSubstrByWidth(modId, maxModIdWidth - font.width("...")) + "...";
            }

            guiGraphics.drawString(font, truncatedModId, this.getX() + 30, this.getY() + 28, 0xCCCCCC);

            // Render recipe count (moved further down)
            String recipeCount = modInfo.getRecipeCount() + " recipes";
            guiGraphics.drawString(font, recipeCount, this.getX() + 30, this.getY() + 44, 0xAAAAA);
        }

        public void renderTooltip(GuiGraphics guiGraphics, int mouseX, int mouseY) {
            // Show tooltip with full information when hovering
            if (this.isHovered()) {
                java.util.List<Component> tooltip = new java.util.ArrayList<>();
                tooltip.add(Component.literal("§f" + modInfo.getDisplayName()));
                tooltip.add(Component.literal("§7Mod ID: " + modInfo.getModId()));
                tooltip.add(Component.literal("§a" + modInfo.getRecipeCount() + " recipes available"));
                tooltip.add(Component.literal("§eClick to select this mod"));

                guiGraphics.renderComponentTooltip(font, tooltip, mouseX, mouseY);
            }
        }
    }
}
