package de.sarocesch.sarosrecipe;

import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;

import de.sarocesch.sarosrecipe.commands.RecipeCommand;
import de.sarocesch.sarosrecipe.util.ModDiscovery;

@Mod("sarosrecipemod")
public class SarosRecipeMod {
    public static final String MODID = "sarosrecipemod";

    public SarosRecipeMod() {
        System.out.println("Saro's Recipe Mod is initializing...");

        FMLJavaModLoadingContext.get().getModEventBus().addListener(this::setup);

        MinecraftForge.EVENT_BUS.register(this);
    }

    private void setup(final FMLCommonSetupEvent event) {
        System.out.println("Saro's Recipe Mod setup complete");
        System.out.println(ModDiscovery.getModSummary());
    }

    @SubscribeEvent
    public void onRegisterCommands(RegisterCommandsEvent event) {
        System.out.println("Registering modrecipe commands");
        RecipeCommand.register(event.getDispatcher());
    }
}
