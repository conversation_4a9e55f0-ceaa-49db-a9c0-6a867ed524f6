{"commands.sarosrecipe.give.success.single": "Successfully gave recipe %2$s to %1$s", "commands.sarosrecipe.give.success.mod": "Successfully gave %2$d recipes from mod %3$s to %1$s", "commands.sarosrecipe.take.success.single": "Successfully removed recipe %2$s from %1$s", "commands.sarosrecipe.take.success.mod": "Successfully removed %2$d recipes from mod %3$s from %1$s", "commands.sarosrecipe.give.error": "Error giving recipe: %s", "commands.sarosrecipe.take.error": "Error taking recipe: %s", "commands.sarosrecipe.recipe.not_found": "Recipe not found: %s", "commands.sarosrecipe.recipe.invalid": "Invalid recipe format: %s", "commands.sarosrecipe.mod.no_recipes": "No recipes found for mod: %s"}