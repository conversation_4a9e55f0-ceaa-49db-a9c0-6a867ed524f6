{"commands.sarosrecipe.give.success.single": "Successfully gave recipe %2$s to %1$s", "commands.sarosrecipe.give.success.mod": "Successfully gave %2$d recipes from mod %3$s to %1$s", "commands.sarosrecipe.take.success.single": "Successfully removed recipe %2$s from %1$s", "commands.sarosrecipe.take.success.mod": "Successfully removed %2$d recipes from mod %3$s from %1$s", "commands.sarosrecipe.give.error": "Error giving recipe: %s", "commands.sarosrecipe.take.error": "Error taking recipe: %s", "commands.sarosrecipe.recipe.not_found": "Recipe not found: %s", "commands.sarosrecipe.recipe.invalid": "Invalid recipe format: %s", "commands.sarosrecipe.mod.no_recipes": "No recipes found for mod: %s", "config.sarosrecipe.enable_first_time_recipe_removal": "Enable First-Time Recipe <PERSON>", "config.sarosrecipe.enable_first_time_mod_selection_gui": "Enable First-Time Mod Selection GUI", "message.sarosrecipe.first_time_recipes_removed": "Welcome! All recipes have been removed. Please select a mod to get started.", "message.sarosrecipe.first_time_welcome": "Welcome to the server! Please select a mod to receive its recipes.", "gui.sarosrecipe.mod_selection.title": "Select a Mod", "gui.sarosrecipe.mod_selection.subtitle": "Choose a mod to receive all its recipes", "gui.sarosrecipe.mod_selection.prev_page": "Previous", "gui.sarosrecipe.mod_selection.next_page": "Next", "gui.sarosrecipe.mod_selection.page_info": "Page %d of %d"}