package de.sarocesch.sarosrecipe.events;

import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.network.PacketDistributor;

import de.sarocesch.sarosrecipe.SarosRecipeMod;
import de.sarocesch.sarosrecipe.config.SarosRecipeConfig;
import de.sarocesch.sarosrecipe.data.PlayerDataManager;
import de.sarocesch.sarosrecipe.network.NetworkHandler;
import de.sarocesch.sarosrecipe.network.packets.OpenModSelectionGuiPacket;
import de.sarocesch.sarosrecipe.services.RecipeService;

/**
 * Handles player join events for first-time join features
 */
@Mod.EventBusSubscriber(modid = SarosRecipeMod.MODID)
public class PlayerJoinHandler {
    
    @SubscribeEvent
    public static void onPlayerJoin(PlayerEvent.PlayerLoggedInEvent event) {
        if (!(event.getEntity() instanceof ServerPlayer player)) {
            return;
        }
        
        PlayerDataManager dataManager = PlayerDataManager.get();
        
        // Check if this is the player's first time joining
        if (dataManager.isFirstTimeJoin(player)) {
            System.out.println("First-time join detected for player: " + player.getName().getString());
            
            // Mark player as having joined
            dataManager.markPlayerAsJoined(player);
            
            // Handle first-time recipe removal if enabled
            if (SarosRecipeConfig.ENABLE_FIRST_TIME_RECIPE_REMOVAL.get()) {
                handleFirstTimeRecipeRemoval(player);
            }
            
            // Handle first-time mod selection GUI if enabled
            if (SarosRecipeConfig.ENABLE_FIRST_TIME_MOD_SELECTION_GUI.get()) {
                handleFirstTimeModSelectionGui(player);
            }
        }
    }
    
    private static void handleFirstTimeRecipeRemoval(ServerPlayer player) {
        try {
            // Remove all recipes from the player (equivalent to /modrecipe take <player> *)
            RecipeService.removeAllRecipesFromPlayer(player);
            
            // Send confirmation message to player
            player.sendSystemMessage(Component.translatable("message.sarosrecipe.first_time_recipes_removed"));
            
            System.out.println("Removed all recipes from first-time player: " + player.getName().getString());
        } catch (Exception e) {
            System.err.println("Error removing recipes from first-time player " + player.getName().getString() + ": " + e.getMessage());
        }
    }
    
    private static void handleFirstTimeModSelectionGui(ServerPlayer player) {
        try {
            // Send packet to client to open mod selection GUI
            NetworkHandler.INSTANCE.send(
                PacketDistributor.PLAYER.with(() -> player),
                new OpenModSelectionGuiPacket()
            );
            
            // Send welcome message to player
            player.sendSystemMessage(Component.translatable("message.sarosrecipe.first_time_welcome"));
            
            System.out.println("Opened mod selection GUI for first-time player: " + player.getName().getString());
        } catch (Exception e) {
            System.err.println("Error opening mod selection GUI for first-time player " + player.getName().getString() + ": " + e.getMessage());
        }
    }
}
