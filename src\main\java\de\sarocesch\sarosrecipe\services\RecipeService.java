package de.sarocesch.sarosrecipe.services;

import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.crafting.Recipe;
import net.minecraft.world.item.crafting.RecipeManager;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service class for recipe management operations
 */
public class RecipeService {
    
    /**
     * Remove all recipes from a player (equivalent to /modrecipe take <player> *)
     */
    public static void removeAllRecipesFromPlayer(ServerPlayer player) {
        RecipeManager recipeManager = player.getServer().getRecipeManager();
        Collection<Recipe<?>> allRecipes = recipeManager.getRecipes();
        
        // Convert all recipes to a list for batch removal
        List<Recipe<?>> recipesToRemove = allRecipes.stream().collect(Collectors.toList());
        
        if (!recipesToRemove.isEmpty()) {
            // Remove all recipes from the player
            player.resetRecipes(recipesToRemove);
            
            System.out.println("Removed " + recipesToRemove.size() + " recipes from player: " + player.getName().getString());
        }
    }
    
    /**
     * Give all recipes from a specific mod to a player
     */
    public static void giveAllRecipesFromMod(ServerPlayer player, String modId) {
        RecipeManager recipeManager = player.getServer().getRecipeManager();
        Collection<Recipe<?>> allRecipes = recipeManager.getRecipes();
        
        List<Recipe<?>> modRecipes = allRecipes.stream()
            .filter(recipe -> recipe.getId().getNamespace().equals(modId))
            .collect(Collectors.toList());
        
        if (modRecipes.isEmpty()) {
            player.sendSystemMessage(Component.translatable("commands.sarosrecipe.mod.no_recipes", modId));
            return;
        }
        
        // Give all recipes from the mod to the player
        player.awardRecipes(modRecipes);
        
        // Send success message
        player.sendSystemMessage(Component.translatable("commands.sarosrecipe.give.success.mod",
            player.getName(), modRecipes.size(), modId));
        
        System.out.println("Gave " + modRecipes.size() + " recipes from mod '" + modId + "' to player: " + player.getName().getString());
    }
    
    /**
     * Get the count of recipes available for a specific mod
     */
    public static int getRecipeCountForMod(RecipeManager recipeManager, String modId) {
        Collection<Recipe<?>> allRecipes = recipeManager.getRecipes();
        
        return (int) allRecipes.stream()
            .map(Recipe::getId)
            .filter(id -> id.getNamespace().equals(modId))
            .count();
    }
    
    /**
     * Check if a mod has any recipes available
     */
    public static boolean hasRecipes(RecipeManager recipeManager, String modId) {
        return getRecipeCountForMod(recipeManager, modId) > 0;
    }
}
