package de.sarocesch.sarosrecipe.network;

import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.network.NetworkRegistry;
import net.minecraftforge.network.simple.SimpleChannel;

import de.sarocesch.sarosrecipe.SarosRecipeMod;
import de.sarocesch.sarosrecipe.network.packets.OpenModSelectionGuiPacket;
import de.sarocesch.sarosrecipe.network.packets.SelectModPacket;

/**
 * Handles network communication between client and server
 */
public class NetworkHandler {
    
    private static final String PROTOCOL_VERSION = "1";
    
    public static final SimpleChannel INSTANCE = NetworkRegistry.newSimpleChannel(
        ResourceLocation.fromNamespaceAndPath(SarosRecipeMod.MODID, "main"),
        () -> PROTOCOL_VERSION,
        PROTOCOL_VERSION::equals,
        PROTOCOL_VERSION::equals
    );
    
    private static int packetId = 0;
    
    /**
     * Initialize network handler and register packets
     */
    public static void init() {
        // Server to Client packets
        INSTANCE.registerMessage(
            packetId++,
            OpenModSelectionGuiPacket.class,
            OpenModSelectionGuiPacket::encode,
            OpenModSelectionGuiPacket::decode,
            OpenModSelectionGuiPacket::handle
        );
        
        // Client to Server packets
        INSTANCE.registerMessage(
            packetId++,
            SelectModPacket.class,
            SelectModPacket::encode,
            SelectModPacket::decode,
            SelectModPacket::handle
        );
    }
}
