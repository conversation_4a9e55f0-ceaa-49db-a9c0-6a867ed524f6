package de.sarocesch.sarosrecipe.services;

import net.minecraft.client.Minecraft;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.crafting.RecipeManager;
import net.minecraftforge.registries.ForgeRegistries;

import de.sarocesch.sarosrecipe.util.ModDiscovery;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * Service for collecting and managing mod data for the GUI
 */
public class ModDataService {
    
    /**
     * Represents a mod with its display information
     */
    public static class ModInfo {
        private final String modId;
        private final String displayName;
        private final ItemStack icon;
        private final int recipeCount;
        
        public ModInfo(String modId, String displayName, ItemStack icon, int recipeCount) {
            this.modId = modId;
            this.displayName = displayName;
            this.icon = icon;
            this.recipeCount = recipeCount;
        }
        
        public String getModId() { return modId; }
        public String getDisplayName() { return displayName; }
        public ItemStack getIcon() { return icon; }
        public int getRecipeCount() { return recipeCount; }
    }
    
    /**
     * Get all mods that have recipes available, sorted by display name
     */
    public static List<ModInfo> getModsWithRecipes() {
        List<ModInfo> modsWithRecipes = new ArrayList<>();
        RecipeManager recipeManager = Minecraft.getInstance().level.getRecipeManager();
        
        for (String modId : ModDiscovery.getAllModIds()) {
            int recipeCount = RecipeService.getRecipeCountForMod(recipeManager, modId);
            
            if (recipeCount > 0) {
                String displayName = ModDiscovery.getModDisplayName(modId);
                ItemStack icon = getModIcon(modId);
                
                modsWithRecipes.add(new ModInfo(modId, displayName, icon, recipeCount));
            }
        }
        
        // Sort by display name for consistent ordering
        modsWithRecipes.sort(Comparator.comparing(ModInfo::getDisplayName));
        
        return modsWithRecipes;
    }
    
    /**
     * Get an icon for a mod (attempts to find creative tab icon or falls back to default)
     */
    private static ItemStack getModIcon(String modId) {
        // Try to find the first item from this mod
        Optional<ItemStack> modItem = ForgeRegistries.ITEMS.getValues().stream()
            .filter(item -> ForgeRegistries.ITEMS.getKey(item).getNamespace().equals(modId))
            .map(ItemStack::new)
            .findFirst();

        if (modItem.isPresent()) {
            return modItem.get();
        }

        // Fallback icons based on mod ID
        return getDefaultIcon(modId);
    }
    
    /**
     * Get a default icon based on mod ID
     */
    private static ItemStack getDefaultIcon(String modId) {
        switch (modId) {
            case "minecraft":
                return new ItemStack(Items.GRASS_BLOCK);
            case "forge":
                return new ItemStack(Items.ANVIL);
            default:
                return new ItemStack(Items.BOOK);
        }
    }
}
