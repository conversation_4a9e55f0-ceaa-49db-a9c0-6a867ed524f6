package de.sarocesch.sarosrecipe.data;

import net.minecraft.nbt.CompoundTag;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.saveddata.SavedData;
import net.minecraft.world.level.storage.DimensionDataStorage;
import net.minecraftforge.server.ServerLifecycleHooks;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * Manages player data for tracking first-time joins
 */
public class PlayerDataManager extends SavedData {
    
    private static final String DATA_NAME = "sarosrecipe_player_data";
    private static final String FIRST_TIME_PLAYERS_KEY = "first_time_players";
    
    private final Set<UUID> firstTimeJoinedPlayers = new HashSet<>();
    
    public PlayerDataManager() {
        super();
    }
    
    public PlayerDataManager(CompoundTag tag) {
        this();
        load(tag);
    }
    
    /**
     * Get the PlayerDataManager instance for the current server
     */
    public static PlayerDataManager get() {
        MinecraftServer server = ServerLifecycleHooks.getCurrentServer();
        if (server == null) {
            throw new IllegalStateException("Server is not available");
        }
        
        DimensionDataStorage storage = server.overworld().getDataStorage();
        return storage.computeIfAbsent(PlayerDataManager::new, PlayerDataManager::new, DATA_NAME);
    }
    
    /**
     * Check if a player has joined the server before
     */
    public boolean hasPlayerJoinedBefore(ServerPlayer player) {
        return firstTimeJoinedPlayers.contains(player.getUUID());
    }
    
    /**
     * Mark a player as having joined the server
     */
    public void markPlayerAsJoined(ServerPlayer player) {
        if (firstTimeJoinedPlayers.add(player.getUUID())) {
            setDirty();
        }
    }
    
    /**
     * Check if this is a player's first time joining
     */
    public boolean isFirstTimeJoin(ServerPlayer player) {
        return !hasPlayerJoinedBefore(player);
    }
    
    private void load(CompoundTag tag) {
        firstTimeJoinedPlayers.clear();
        
        if (tag.contains(FIRST_TIME_PLAYERS_KEY)) {
            CompoundTag playersTag = tag.getCompound(FIRST_TIME_PLAYERS_KEY);
            for (String key : playersTag.getAllKeys()) {
                try {
                    UUID playerUUID = UUID.fromString(key);
                    firstTimeJoinedPlayers.add(playerUUID);
                } catch (IllegalArgumentException e) {
                    // Skip invalid UUIDs
                }
            }
        }
    }
    
    @Override
    public CompoundTag save(CompoundTag tag) {
        CompoundTag playersTag = new CompoundTag();
        
        for (UUID playerUUID : firstTimeJoinedPlayers) {
            playersTag.putBoolean(playerUUID.toString(), true);
        }
        
        tag.put(FIRST_TIME_PLAYERS_KEY, playersTag);
        return tag;
    }
}
