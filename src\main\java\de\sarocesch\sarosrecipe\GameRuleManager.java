package de.sarocesch.sarosrecipe;

import net.minecraft.client.Minecraft;
import net.minecraft.world.level.GameRules;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.event.level.LevelEvent;
import net.minecraftforge.event.server.ServerStartedEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@Mod.EventBusSubscriber(modid = SarosRecipeMod.MODID)
public class GameRuleManager {

    @SubscribeEvent
    public static void onServerStarted(ServerStartedEvent event) {
        System.out.println("Server started - checking doLimitedCrafting game rule");
        setLimitedCraftingRule(event.getServer().getGameRules(), event.getServer());
    }

    @SubscribeEvent
    @OnlyIn(Dist.CLIENT)
    public static void onWorldLoad(LevelEvent.Load event) {
        if (event.getLevel().isClientSide()) {
            Minecraft minecraft = Minecraft.getInstance();
            if (minecraft.level != null && minecraft.hasSingleplayerServer()) {
                if (minecraft.getSingleplayerServer() != null) {
                    System.out.println("Singleplayer world loaded - setting doLimitedCrafting");
                    setLimitedCraftingRule(minecraft.getSingleplayerServer().getGameRules(),
                                         minecraft.getSingleplayerServer());
                }
            } else if (minecraft.level != null) {
                GameRules gameRules = minecraft.level.getGameRules();
                GameRules.BooleanValue limitedCrafting = gameRules.getRule(GameRules.RULE_LIMITED_CRAFTING);
                System.out.println("Connected to multiplayer server. doLimitedCrafting is: " + limitedCrafting.get());

                if (!limitedCrafting.get()) {
                    System.out.println("WARNING: doLimitedCrafting is disabled on this server. Recipe management may not work as expected.");
                }
            }
        }
    }

    private static void setLimitedCraftingRule(GameRules gameRules, net.minecraft.server.MinecraftServer server) {
        GameRules.BooleanValue limitedCrafting = gameRules.getRule(GameRules.RULE_LIMITED_CRAFTING);

        if (!limitedCrafting.get()) {
            System.out.println("Setting doLimitedCrafting to true");
            limitedCrafting.set(true, server);
            System.out.println("doLimitedCrafting has been enabled");
        } else {
            System.out.println("doLimitedCrafting is already enabled");
        }
    }
}
