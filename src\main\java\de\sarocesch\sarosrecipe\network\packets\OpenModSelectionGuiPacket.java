package de.sarocesch.sarosrecipe.network.packets;

import net.minecraft.client.Minecraft;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent;

import de.sarocesch.sarosrecipe.client.gui.ModSelectionScreen;

import java.util.function.Supplier;

/**
 * Packet sent from server to client to open the mod selection GUI
 */
public class OpenModSelectionGuiPacket {
    
    public OpenModSelectionGuiPacket() {
        // Empty constructor for packet
    }
    
    public static void encode(OpenModSelectionGuiPacket packet, FriendlyByteBuf buffer) {
        // No data to encode for this packet
    }
    
    public static OpenModSelectionGuiPacket decode(FriendlyByteBuf buffer) {
        return new OpenModSelectionGuiPacket();
    }
    
    public static void handle(OpenModSelectionGuiPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            // Only execute on client side
            DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> () -> {
                Minecraft minecraft = Minecraft.getInstance();
                minecraft.setScreen(new ModSelectionScreen());
            });
        });
        context.setPacketHandled(true);
    }
}
