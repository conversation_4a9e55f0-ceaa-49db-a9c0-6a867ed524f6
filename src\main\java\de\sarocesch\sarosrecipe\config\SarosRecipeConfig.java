package de.sarocesch.sarosrecipe.config;

import net.minecraftforge.common.ForgeConfigSpec;

public class SarosRecipeConfig {
    
    public static final ForgeConfigSpec.Builder BUILDER = new ForgeConfigSpec.Builder();
    public static final ForgeConfigSpec SPEC;
    
    // First-time join features configuration
    public static final ForgeConfigSpec.BooleanValue ENABLE_FIRST_TIME_RECIPE_REMOVAL;
    public static final ForgeConfigSpec.BooleanValue ENABLE_FIRST_TIME_MOD_SELECTION_GUI;
    
    static {
        BUILDER.push("first_time_join");
        
        ENABLE_FIRST_TIME_RECIPE_REMOVAL = BUILDER
            .comment("Enable automatic recipe removal when a player joins the server for the first time")
            .translation("config.sarosrecipe.enable_first_time_recipe_removal")
            .define("enableFirstTimeRecipeRemoval", true);
            
        ENABLE_FIRST_TIME_MOD_SELECTION_GUI = BUILDER
            .comment("Enable mod selection GUI when a player joins the server for the first time")
            .translation("config.sarosrecipe.enable_first_time_mod_selection_gui")
            .define("enableFirstTimeModSelectionGui", true);
        
        BUILDER.pop();
        
        SPEC = BUILDER.build();
    }
}
