package de.sarocesch.sarosrecipe.util;

import net.minecraftforge.fml.ModList;
import net.minecraftforge.forgespi.language.IModInfo;

import java.util.Set;
import java.util.stream.Collectors;

public class ModDiscovery {

    public static Set<String> getAllModIds() {
        Set<String> modIds = ModList.get().getMods().stream()
            .map(IModInfo::getModId)
            .collect(Collectors.toSet());
        
        modIds.add("minecraft");
        
        return modIds;
    }

    public static Set<String> getModIdsStartingWith(String prefix) {
        String lowerPrefix = prefix.toLowerCase();
        
        return getAllModIds().stream()
            .filter(modId -> modId.toLowerCase().startsWith(lowerPrefix))
            .collect(Collectors.toSet());
    }
    

    public static boolean isModLoaded(String modId) {
        if ("minecraft".equals(modId)) {
            return true;
        }
        
        return ModList.get().isLoaded(modId);
    }
    

    public static String getModDisplayName(String modId) {
        if ("minecraft".equals(modId)) {
            return "Minecraft (Vanilla)";
        }

        return ModList.get().getModContainerById(modId)
            .map(container -> container.getModInfo().getDisplayName())
            .orElse(modId);
    }


    public static int getLoadedModCount() {
        return ModList.get().size();
    }

    public static String getModSummary() {
        Set<String> modIds = getAllModIds();
        int totalMods = modIds.size();

        StringBuilder summary = new StringBuilder();
        summary.append("Loaded ").append(totalMods).append(" mods: ");

        modIds.stream()
            .limit(5)
            .forEach(modId -> summary.append(modId).append(", "));

        if (totalMods > 5) {
            summary.append("...");
        } else {
            summary.setLength(summary.length() - 2);
        }

        return summary.toString();
    }
}
