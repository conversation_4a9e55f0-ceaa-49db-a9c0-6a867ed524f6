{"commands.sarosrecipe.give.success.single": "Rezept %2$s erfolgreich an %1$s gegeben", "commands.sarosrecipe.give.success.mod": "%2$d Rezept<PERSON> von Mod %3$s erfolgreich an %1$s gegeben", "commands.sarosrecipe.take.success.single": "Rezept %2$s erfolg<PERSON>ich von %1$s entfernt", "commands.sarosrecipe.take.success.mod": "%2$d Rezepte von Mod %3$s erfolg<PERSON><PERSON> von %1$s entfernt", "commands.sarosrecipe.give.error": "Fehler beim Geben des Rezepts: %s", "commands.sarosrecipe.take.error": "Fehler beim Entfernen des Rezepts: %s", "commands.sarosrecipe.recipe.not_found": "Rezept nicht gefunden: %s", "commands.sarosrecipe.recipe.invalid": "Ungültiges Rezeptformat: %s", "commands.sarosrecipe.mod.no_recipes": "<PERSON>ine Rezepte für Mod gefunden: %s", "config.sarosrecipe.enable_first_time_recipe_removal": "Erstes Mal Rezept-Entfernung aktivieren", "config.sarosrecipe.enable_first_time_mod_selection_gui": "Erstes Mal Mod-Auswahl GUI aktivieren", "message.sarosrecipe.first_time_recipes_removed": "Willkommen! Alle Rezepte wurden entfernt. Bitte wählen Si<PERSON> eine Mod aus, um zu beginnen.", "message.sarosrecipe.first_time_welcome": "Willkommen auf dem Server! Bitte wählen Sie eine Mod aus, um deren Rezepte zu erhalten.", "gui.sarosrecipe.mod_selection.title": "<PERSON><PERSON> au<PERSON>w<PERSON>", "gui.sarosrecipe.mod_selection.subtitle": "<PERSON><PERSON><PERSON>en Si<PERSON> eine Mod aus, um alle ihre Rezepte zu erhalten", "gui.sarosrecipe.mod_selection.prev_page": "Zurück", "gui.sarosrecipe.mod_selection.next_page": "<PERSON><PERSON>", "gui.sarosrecipe.mod_selection.page_info": "Seite %d von %d"}