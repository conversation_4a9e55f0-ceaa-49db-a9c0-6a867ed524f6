package de.sarocesch.sarosrecipe.commands;

import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.suggestion.SuggestionProvider;
import com.mojang.brigadier.suggestion.Suggestions;
import com.mojang.brigadier.suggestion.SuggestionsBuilder;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.network.chat.Component;

import de.sarocesch.sarosrecipe.util.ModDiscovery;

import java.util.concurrent.CompletableFuture;


public class ModIdSuggestionProvider implements SuggestionProvider<CommandSourceStack> {
    
    public static final ModIdSuggestionProvider INSTANCE = new ModIdSuggestionProvider();
    
    @Override
    public CompletableFuture<Suggestions> getSuggestions(CommandContext<CommandSourceStack> context, SuggestionsBuilder builder) {
        String input = builder.getRemaining();

        if (input.contains(":")) {
            return builder.buildFuture();
        }

        String lowerInput = input.toLowerCase();
        ModDiscovery.getModIdsStartingWith(lowerInput).forEach(modId -> {
            String displayName = ModDiscovery.getModDisplayName(modId);
            if (!modId.equals(displayName)) {
                builder.suggest(modId, Component.literal(displayName));
            } else {
                builder.suggest(modId);
            }
        });

        return builder.buildFuture();
    }

}
