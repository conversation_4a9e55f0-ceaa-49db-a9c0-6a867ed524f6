package de.sarocesch.sarosrecipe.commands;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.crafting.Recipe;
import net.minecraft.world.item.crafting.RecipeManager;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class RecipeCommand {

    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("modrecipe")
            .requires(source -> source.hasPermission(2)) // Requires OP level 2
            .then(Commands.literal("give")
                .then(Commands.argument("player", EntityArgument.player())
                    .then(Commands.argument("recipe_path", StringArgumentType.greedyString())
                        .suggests(ModIdSuggestionProvider.INSTANCE)
                        .executes(RecipeCommand::giveRecipe))))
            .then(Commands.literal("take")
                .then(Commands.argument("player", EntityArgument.player())
                    .then(Commands.argument("recipe_path", StringArgumentType.greedyString())
                        .suggests(ModIdSuggestionProvider.INSTANCE)
                        .executes(RecipeCommand::takeRecipe)))));
    }

    private static int giveRecipe(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        ServerPlayer player = EntityArgument.getPlayer(context, "player");
        String recipePath = StringArgumentType.getString(context, "recipe_path");
        CommandSourceStack source = context.getSource();

        try {
            if (recipePath.contains(":")) {
                return giveSingleRecipe(source, player, recipePath);
            } else {
                return giveAllRecipesFromMod(source, player, recipePath);
            }
        } catch (Exception e) {
            source.sendFailure(Component.translatable("commands.sarosrecipe.give.error", e.getMessage()));
            return 0;
        }
    }

    private static int takeRecipe(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        ServerPlayer player = EntityArgument.getPlayer(context, "player");
        String recipePath = StringArgumentType.getString(context, "recipe_path");
        CommandSourceStack source = context.getSource();

        try {
            if (recipePath.contains(":")) {
                return takeSingleRecipe(source, player, recipePath);
            } else {
                return takeAllRecipesFromMod(source, player, recipePath);
            }
        } catch (Exception e) {
            source.sendFailure(Component.translatable("commands.sarosrecipe.take.error", e.getMessage()));
            return 0;
        }
    }

    private static int giveSingleRecipe(CommandSourceStack source, ServerPlayer player, String recipePath) {
        try {
            ResourceLocation recipeId = ResourceLocation.tryParse(recipePath);
            if (recipeId == null) {
                source.sendFailure(Component.translatable("commands.sarosrecipe.recipe.invalid", recipePath));
                return 0;
            }

            RecipeManager recipeManager = source.getServer().getRecipeManager();

            if (recipeManager.byKey(recipeId).isPresent()) {
                Recipe<?> recipe = recipeManager.byKey(recipeId).get();
                player.awardRecipes(List.of(recipe));
                source.sendSuccess(() -> Component.translatable("commands.sarosrecipe.give.success.single",
                    player.getName(), recipePath), true);
                return 1;
            } else {
                source.sendFailure(Component.translatable("commands.sarosrecipe.recipe.not_found", recipePath));
                return 0;
            }
        } catch (Exception e) {
            source.sendFailure(Component.translatable("commands.sarosrecipe.recipe.invalid", recipePath));
            return 0;
        }
    }

    private static int takeSingleRecipe(CommandSourceStack source, ServerPlayer player, String recipePath) {
        try {
            ResourceLocation recipeId = ResourceLocation.tryParse(recipePath);
            if (recipeId == null) {
                source.sendFailure(Component.translatable("commands.sarosrecipe.recipe.invalid", recipePath));
                return 0;
            }

            RecipeManager recipeManager = source.getServer().getRecipeManager();

            if (recipeManager.byKey(recipeId).isPresent()) {
                Recipe<?> recipe = recipeManager.byKey(recipeId).get();
                player.resetRecipes(List.of(recipe));
                source.sendSuccess(() -> Component.translatable("commands.sarosrecipe.take.success.single",
                    player.getName(), recipePath), true);
                return 1;
            } else {
                source.sendFailure(Component.translatable("commands.sarosrecipe.recipe.not_found", recipePath));
                return 0;
            }
        } catch (Exception e) {
            source.sendFailure(Component.translatable("commands.sarosrecipe.recipe.invalid", recipePath));
            return 0;
        }
    }

    private static int giveAllRecipesFromMod(CommandSourceStack source, ServerPlayer player, String modId) {
        RecipeManager recipeManager = source.getServer().getRecipeManager();
        Collection<Recipe<?>> allRecipes = recipeManager.getRecipes();

        List<ResourceLocation> modRecipes = allRecipes.stream()
            .map(Recipe::getId)
            .filter(id -> id.getNamespace().equals(modId))
            .collect(Collectors.toList());

        if (modRecipes.isEmpty()) {
            source.sendFailure(Component.translatable("commands.sarosrecipe.mod.no_recipes", modId));
            return 0;
        }

        for (ResourceLocation recipeId : modRecipes) {
            recipeManager.byKey(recipeId).ifPresent(recipe -> {
                player.awardRecipes(List.of(recipe));
            });
        }

        source.sendSuccess(() -> Component.translatable("commands.sarosrecipe.give.success.mod",
            player.getName(), modRecipes.size(), modId), true);
        return modRecipes.size();
    }

    private static int takeAllRecipesFromMod(CommandSourceStack source, ServerPlayer player, String modId) {
        RecipeManager recipeManager = source.getServer().getRecipeManager();
        Collection<Recipe<?>> allRecipes = recipeManager.getRecipes();

        List<ResourceLocation> modRecipes = allRecipes.stream()
            .map(Recipe::getId)
            .filter(id -> id.getNamespace().equals(modId))
            .collect(Collectors.toList());

        if (modRecipes.isEmpty()) {
            source.sendFailure(Component.translatable("commands.sarosrecipe.mod.no_recipes", modId));
            return 0;
        }

        for (ResourceLocation recipeId : modRecipes) {
            recipeManager.byKey(recipeId).ifPresent(recipe -> {
                player.resetRecipes(List.of(recipe));
            });
        }

        source.sendSuccess(() -> Component.translatable("commands.sarosrecipe.take.success.mod",
            player.getName(), modRecipes.size(), modId), true);
        return modRecipes.size();
    }
}
