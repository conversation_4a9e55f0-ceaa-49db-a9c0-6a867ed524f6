package de.sarocesch.sarosrecipe.network.packets;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkEvent;

import de.sarocesch.sarosrecipe.services.RecipeService;

import java.util.function.Supplier;

/**
 * Packet sent from client to server when a player selects a mod in the GUI
 */
public class SelectModPacket {
    
    private final String selectedModId;
    
    public SelectModPacket(String selectedModId) {
        this.selectedModId = selectedModId;
    }
    
    public static void encode(SelectModPacket packet, FriendlyByteBuf buffer) {
        buffer.writeUtf(packet.selectedModId);
    }
    
    public static SelectModPacket decode(FriendlyByteBuf buffer) {
        String selectedModId = buffer.readUtf();
        return new SelectModPacket(selectedModId);
    }
    
    public static void handle(SelectModPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            ServerPlayer player = context.getSender();
            if (player != null) {
                // Give all recipes from the selected mod to the player
                RecipeService.giveAllRecipesFromMod(player, packet.selectedModId);
            }
        });
        context.setPacketHandled(true);
    }
}
